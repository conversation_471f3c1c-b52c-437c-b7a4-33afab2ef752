import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/base/base_stateful_page.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/models/entities/commission_overview_entity.dart';
import 'package:wd/core/models/entities/my_team_entity.dart';
import 'package:wd/core/models/entities/team_entity.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/utils/string_util.dart';
import 'package:wd/features/page/4_mine/promotion_rewards/promotion_rewards_cubit.dart';
import 'package:wd/features/routers/app_router.dart';
import 'package:wd/features/routers/navigator_utils.dart';
import 'package:wd/injection_container.dart';
import 'package:wd/shared/mixin/page_view_animation_mixin.dart';
import 'package:wd/shared/widgets/app_image.dart';
import 'package:wd/shared/widgets/button/gradient_button.dart';
import 'package:wd/shared/widgets/common_tabbar.dart';
import 'package:wd/shared/widgets/easy_loading.dart';
import 'package:wd/shared/widgets/promotion/promotion_share.dart';
import 'package:wd/shared/widgets/section_container.dart';
import 'promotion_rewards_state.dart';

/// 推广赚钱
class PromotionRewardsView extends BasePage {
  const PromotionRewardsView({super.key});

  @override
  BasePageState<BasePage> getState() => _PromotionRewardsViewState();
}

class _PromotionRewardsViewState extends BasePageState {

  @override
  void initState() {
    super.initState();
    isBack = false;
    pageTitle = "promotion".tr();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PromotionRewardsCubit>().didPop();
      context.read<PromotionRewardsCubit>().fetchUserInviteLink();
      context.read<PromotionRewardsCubit>().fetchTeam();
      context.read<PromotionRewardsCubit>().fetchMyTeam();
      context.read<PromotionRewardsCubit>().fetchCommission();
    });
  }

  @override
  Widget buildPage(BuildContext context) {

    final imageActiveColor = context.theme.primaryColor;
    final imageInactiveColor = context.colorTheme.tabInactive;
    final tabs = [
      CommonTabBarItem(title: 'rewards'.tr(), imageUrl: 'assets/images/promotion/icon_reward.svg', imageActiveColor: imageActiveColor, imageInactiveColor: imageInactiveColor),
      CommonTabBarItem(title: 'invite'.tr(), imageUrl: 'assets/images/promotion/icon_invite.svg', imageActiveColor: imageActiveColor, imageInactiveColor: imageInactiveColor),
      CommonTabBarItem(title: 'rules'.tr(), imageUrl: 'assets/images/promotion/icon_rules.svg', imageActiveColor: imageActiveColor, imageInactiveColor: imageInactiveColor),
    ];


    return BlocBuilder<PromotionRewardsCubit, PromotionRewardsState>(
      builder: (context, state) {
        return Scaffold(
          body: SingleChildScrollView(
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.gw),
                child: AnimationLimiter(
                  child: Column(
                    children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 375),
                      childAnimationBuilder: (widget) => SlideAnimation(
                        horizontalOffset: 50.0,
                        child: FadeInAnimation(
                          child: widget,
                        ),
                      ),
                      children: [
                        SizedBox(height: 14.gw),
                        CommonTabBar(
                          tabs,
                          isScrollable: false,
                          currentIndex: state.currentTabIndex,
                          onTap: (index) => context.read<PromotionRewardsCubit>().changeTabIndex(index),
                        ),
                        // _buildTabSection(),
                        SizedBox(height: 10.gw),
                        if (state.currentTabIndex == 0) ...[
                          ...[
                            _buildTeamLevelSection(),
                            SizedBox(height: 14.gw),
                            _buildRewardsOverviewSection(),
                            SizedBox(height: 14.gw),
                            _buildTeamOverviewSection(),
                          ],
                        ],
                        if (state.currentTabIndex == 1) ...[
                          const PromotionSharePage(),
                        ],
                        if (state.currentTabIndex == 2) ...[
                          SingleChildScrollView(
                            child: AppImage(
                                width: double.infinity,
                                fit: BoxFit.fill,
                                imageUrl:
                                    "https://zhz-resource.s3.me-central-1.amazonaws.com/client/promo/promo-rule.png"),
                          ),
                        ],
                      ],
                    ),
                  ),
                )),
          ),
        );
      },
    );
  }

  // Widget _buildTabSection() {
  //   return BlurContainer(
  //     height: 35.gw,
  //     borderRadius: BorderRadius.circular(8.gw),
  //     borderColor: Colors.white,
  //     borderWidth: 1,
  //     gradientColors: const [
  //       Color(0xFFECEFF6),
  //       Color(0xFFFEFEFF),
  //     ],
  //     boxShadow: const BoxShadow(
  //       color: Color(0x1A000000), // 阴影颜色，带透明度
  //       offset: Offset(0, 2), // 阴影偏移
  //       blurRadius: 2, // 模糊半径
  //     ),
  //     child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, int>(
  //       selector: (state) {
  //         return state.currentTabIndex;
  //       },
  //       builder: (context, state) {
  //         return Padding(
  //           padding: EdgeInsets.symmetric(horizontal: 5.gw),
  //           child: Row(
  //             children: tabs
  //                 .map((tab) => _buildTabButton(text: tab.tr(), index: tabs.indexOf(tab), currentTabIndex: state))
  //                 .toList(),
  //           ),
  //         );
  //       },
  //     ),
  //   );
  // }

  // Widget _buildTabButton({required String text, required int index, required int currentTabIndex}) {
  //   return Expanded(
  //       child: SizedBox(
  //     height: 26.gw,
  //     child: GestureDetector(
  //       onTap: () => context.read<PromotionRewardsCubit>().changeTabIndex(index),
  //       child: Container(
  //         height: 26.gw,
  //         decoration: BoxDecoration(
  //           color: index == currentTabIndex ? const Color(0xFFD4B88C) : Colors.transparent,
  //           borderRadius: BorderRadius.circular(6.gw),
  //         ),
  //         child: Center(
  //           child: Text(
  //             text,
  //             textAlign: TextAlign.center,
  //             style: TextStyle(
  //               color: index == currentTabIndex ? Colors.white : const Color(0xff3B4165),
  //               fontSize: 14.fs,
  //               fontWeight: FontWeight.w500,
  //             ),
  //           ),
  //         ),
  //       ),
  //     ),
  //   ));
  // }

  Widget _buildTeamLevelSection() {
    return SectionContainer(
      title: 'my_team_level'.tr(),
      imagePath: "assets/images/promotion/icon_promotion_level.png",
      onTap: () => sl<NavigatorService>().push(AppRouter.commisionRules),
      suffixIcon: Container(
          alignment: Alignment.topCenter,
          child: Image.asset("assets/images/promotion/icon_promotion_plan_tag.png", width: 53.gw)),
      child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, (NetState, TeamEntity?)>(
        selector: (state) => (state.teamNetState, state.teamEntity),
        builder: (context, state) {
          if (state.$2 != null) {
            return _PageViewCard(teamData: state.$2);
          }
          return SizedBox(
              height: 185.gw,
              child: Center(
                  child: SizedBox(
                height: 25.gw,
                width: 25.gw,
                child: const CircularProgressIndicator(strokeWidth: 1),
              )));
        },
      ),
    );
  }

  Widget _buildRewardsOverviewSection() {
    return SectionContainer(
      title: 'commission_overview'.tr(),
      imagePath: 'assets/images/promotion/icon_promotion_title_reward.png',
      onTap: () => sl<NavigatorService>().push(AppRouter.commissionRecords),
      suffixIcon: Padding(
        padding: EdgeInsets.only(right: 12.gw),
        child: Icon(
          Icons.arrow_forward_ios,
          size: 16.fs,
          color: const Color(0XFFCACDDB),
        ),
      ),
      child: SizedBox(
        height: 153.gw,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 18.gw, vertical: 12.gw),
          child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, (NetState, CommissionOverviewEntity?)>(
            selector: (state) => (state.commissionOverviewNetState, state.commissionOverviewEntity),
            builder: (context, state) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: _buildRewardInfoItem(
                            label: 'cumulative_rewards'.tr(), value: (state.$2?.totalCommission ?? '0').toString()),
                      ),
                      SizedBox(width: 78.gw),
                      Expanded(
                        child: _buildRewardInfoItem(
                            label: 'yesterday_rewards'.tr(), value: (state.$2?.yesterdayCommission ?? '0').toString()),
                      ),
                    ],
                  ),
                  SizedBox(height: 17.gw),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: _buildRewardInfoItem(
                            label: 'pending_rewards'.tr(),
                            value: (state.$2?.commission ?? '0').toString(),
                            valueStyle: TextStyle(
                                fontSize: 20.fs, fontWeight: FontWeight.bold, color: const Color(0xff6A7391))),
                      ),
                      SizedBox(width: 78.gw),
                      Expanded(
                          child: BlocListener<PromotionRewardsCubit, PromotionRewardsState>(
                        listenWhen: (previous, current) =>
                            previous.commissionReceiveState != current.commissionReceiveState,
                        listener: (context, state) {
                          if (state.commissionReceiveState == NetState.dataSuccessState) {
                            GSEasyLoading.showToast('commission_receive_success'.tr());
                            context.read<PromotionRewardsCubit>().fetchCommission();
                          }
                        },
                        child: GradientButton(
                          height: 32.gw,
                          width: 114.gw,
                          textColor: Colors.white,
                          gradientColors: const [
                            Color(0xFFEACA9F),
                            Color(0xFFB9936D),
                          ],
                          title: "immediate_withdrawal".tr(),
                          onPressed: () {
                            if ((state.$2?.commission ?? 0) > 0) {
                              context.read<PromotionRewardsCubit>().commissionReceive();
                            } else {
                              GSEasyLoading.showToast('no_commission_available'.tr());
                            }
                          },
                        ),
                      )),
                    ],
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildRewardInfoItem({required String label, required String value, TextStyle? valueStyle}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 14.fs, color: const Color(0xFF6A7391))),
        SizedBox(height: 10.gw),
        Text(
          value,
          style: valueStyle ??
              TextStyle(
                fontSize: 14.fs,
                fontWeight: FontWeight.bold,
                color: const Color(0xff6A7391),
              ),
        ),
      ],
    );
  }

  Widget _buildTeamOverviewSection() {
    return SectionContainer(
      title: 'team_overview'.tr(),
      imagePath: 'assets/images/promotion/icon_promotion_title_reward.png',
      onTap: () => sl<NavigatorService>().push(AppRouter.teamManagement),
      suffixIcon: Padding(
        padding: EdgeInsets.only(right: 12.gw),
        child: Icon(
          Icons.arrow_forward_ios,
          size: 16.fs,
          color: const Color(0XFFCACDDB),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.gw, vertical: 12.gw),
        child: BlocSelector<PromotionRewardsCubit, PromotionRewardsState, (NetState, MyTeamEntity?)>(
          selector: (state) => (state.myTeamNetState, state.myTeamEntity),
          builder: (context, state) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildTeamStat('team_member_count'.tr(), (state.$2?.teamSize ?? '0').toString()),
                _buildTeamStat('team_betting'.tr(), (state.$2?.teamBetAmount ?? '0').toString()),
                _buildTeamStat('team_recharge'.tr(), (state.$2?.teamCashinAmount ?? '0').toString()),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildTeamStat(String label, String value) {
    return Column(
      children: [
        Text(label, style: TextStyle(fontSize: 14.fs, color: const Color(0xFF6A7391))),
        SizedBox(height: 4.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 14.fs,
            fontWeight: FontWeight.w600,
            color: const Color(0xff6A7391),
          ),
        ),
      ],
    );
  }
}

class _PageViewCard extends StatefulWidget {
  final TeamEntity? teamData;

  const _PageViewCard({required this.teamData});

  @override
  State<_PageViewCard> createState() => _PageViewCardState();
}

class _PageViewCardState extends State<_PageViewCard> with PageViewAnimationMixin {
  @override
  void initState() {
    initializePageController(initialPage: widget.teamData?.userTeamLevel ?? 0);
    context.read<PromotionRewardsCubit>().changeTeamLevelIndex(widget.teamData?.userTeamLevel ?? 0);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        buildAnimatedPageView(
          initialPage: widget.teamData?.userTeamLevel ?? 0,
          itemCount: widget.teamData?.teamConfigList?.length ?? 0,
          height: 185.gw,
          itemBuilder: (context, index) => _buildTeamLevelCards(
              teamData: widget.teamData, index: index, currentTeamLevelIndex: widget.teamData?.userTeamLevel ?? 0),
          onPageChanged: (index) => context.read<PromotionRewardsCubit>().changeTeamLevelIndex(index),
        ),
        Positioned(
          bottom: 16.gw,
          left: 113.gw,
          right: 113.gw,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.arrow_back_ios, size: 16.fs, color: const Color(0xFFCACDDB)),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  4,
                  (index) => BlocSelector<PromotionRewardsCubit, PromotionRewardsState, int>(
                    selector: (state) => state.currentTeamLevelIndex,
                    builder: (context, currentIndex) {
                      return Container(
                        width: 12.gw,
                        height: 12.gw,
                        margin: EdgeInsets.symmetric(horizontal: 4.gw),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: currentIndex == index ? const Color(0xFF6A7391) : const Color(0xFFD9D9D9),
                        ),
                      );
                    },
                  ),
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16.fs, color: const Color(0xFFCACDDB)),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildTeamLevelCards({required TeamEntity? teamData, required int index, required int currentTeamLevelIndex}) {
    bool isActive = index == currentTeamLevelIndex;
    TeamTeamConfigList? model = teamData?.teamConfigList?[index];
    if (model == null) return const SizedBox.shrink();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.gw),
      child: Column(
        children: [
          SizedBox(height: 9.gw),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('team_level'.tr(), style: TextStyle(fontSize: 14.fs)),
              SizedBox(width: 8.gw),
              Container(
                  height: 14.gw,
                  width: 33.gw,
                  decoration: BoxDecoration(
                    color: index == currentTeamLevelIndex ? const Color(0xFFD4B88C) : const Color(0xFFD9D9D9),
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                  child: Center(
                      child: Text('LV${(model.teamLevel ?? '0').toString()}',
                          style: TextStyle(fontSize: 10.fs, color: Colors.white)))),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                  child: _buildRewardCard(
                'betting_rewards'.tr(),
                '${((model.betRebate ?? 0) * 100).removeZeros}%',
                isActive ? Assets.iconCasino : Assets.iconCasinoInactive,
              )),
              Expanded(
                child: _buildRewardCard('recharge_rewards'.tr(), '${((model.cashinRebate ?? 0) * 100).removeZeros}%',
                    isActive ? Assets.iconCard : Assets.iconCardInactive),
              ),
              Expanded(
                child: _buildRewardCard('highest_rewards'.tr(), '${model.maxAmount?.removeZeros}',
                    isActive ? Assets.iconGift : Assets.iconGiftInactive),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRewardCard(String title, String value, String imagePath) {
    return SizedBox(
      width: 114.gw,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            height: 60.gw,
            width: 74.gw,
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(fontSize: 12.fs),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12.fs,
            ),
          ),
          SizedBox(height: 14.gw),
        ],
      ),
    );
  }
}
