import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:wd/core/base/base_state.dart';
import 'package:wd/core/constants/assets.dart';
import 'package:wd/core/constants/enums.dart';
import 'package:wd/core/utils/font_size.dart';
import 'package:wd/core/utils/screenUtil.dart';
import 'package:wd/core/theme/themes.dart';
import 'package:wd/shared/widgets/animation/scale_animation.dart';
import 'package:wd/shared/widgets/text_fields/phone_input_field.dart';
import 'package:wd/shared/widgets/common_button.dart';
import 'package:wd/shared/widgets/icon_textfield.dart';
import 'package:wd/core/models/country.dart';
import 'package:wd/core/services/country_service.dart';
import 'package:wd/shared/widgets/verification_code_input_row.dart';

import '../../../../core/utils/system_util.dart';
import 'forgot_cubit.dart';

class ForgotPage extends StatefulWidget {
  const ForgotPage({super.key});

  @override
  State<ForgotPage> createState() => _ForgotPageState();
}

class _ForgotPageState extends State<ForgotPage> {
  Country? _selectedCountry;

  static const double _verticalSpacing = 15.0;
  static const double _sectionSpacing = 30.0;

  @override
  void initState() {
    super.initState();
    _initializeCountry();
  }

  Future<void> _initializeCountry() async {
    final defaultCountry = await CountryService.instance.getDefaultCountry();
    if (mounted) {
      setState(() {
        _selectedCountry = defaultCountry;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      body: _buildSliverLayout(),
    );
  }

 
  Widget _buildSliverLayout() {
    final screenHeight = MediaQuery.of(context).size.height;
    final headerHeight = screenHeight * 0.36; 

    return CustomScrollView(
      slivers: [
        // Header section with title
        SliverPersistentHeader(
          pinned: false,
          floating: false,
          delegate: _ForgotHeaderDelegate(
            minHeight: headerHeight * 0.6, // Minimum height when collapsed
            maxHeight: headerHeight, // Full height when expanded
            title: _buildTitle(),
            subtitle: _buildSubtitle(),
          ),
        ),
        // Content section
        SliverToBoxAdapter(
          child: Container(
            decoration: BoxDecoration(
              color: context.theme.scaffoldBackgroundColor,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 40.gw),
              child: Column(
                children: [
                  SizedBox(height: 20.gw),
                  // Tab bar for phone/email selection
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      final currentIndex = state.forgotType == LoginType.phone ? 0 : 1;
                      return Center(
                        child: Container(
                          width: 200.gw,
                          height: 44.gw,
                          decoration: BoxDecoration(
                            color: context.colorTheme.foregroundColor,
                            borderRadius: BorderRadius.circular(12.gw),
                          ),
                          padding: EdgeInsets.all(4.gw),
                          child: Row(
                            children: [
                              _buildCustomTabItem('Phone', Assets.loginTabIcon, currentIndex == 0, () {
                                context.read<ForgotCubit>().updateForgotType(LoginType.phone);
                              }),
                              _buildCustomTabItem('Email', Assets.registerTabIcon, currentIndex == 1, () {
                                context.read<ForgotCubit>().updateForgotType(LoginType.email);
                              }),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                  SizedBox(height: _sectionSpacing.gw),
                  // Form content
                  BlocBuilder<ForgotCubit, ForgotState>(
                    builder: (context, state) {
                      return CommonScaleAnimationWidget(
                        children: [
                          if (state.forgotType == LoginType.phone) ...[
                            _buildPhoneInputField(state),
                          ] else ...[
                            _buildEmailInputField(state),
                          ],
                          SizedBox(height: _verticalSpacing.gw),
                          _buildVerificationCodeInputRow(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildPasswordInput(state),
                          SizedBox(height: _verticalSpacing.gw),
                          _buildConfirmPasswordInput(state),
                          SizedBox(height: _sectionSpacing.gw),
                          _buildResetButton(),
                          SizedBox(height: 20.gw),
                          _buildBottomText(),
                          SizedBox(height: 40.gw), 
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Builds the title for the header
  Widget _buildTitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return Text(
          'Reset',
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.w400,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the subtitle for the header
  Widget _buildSubtitle() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        final forgotType = state.forgotType;
        String subtitle;

        switch (forgotType) {
          case LoginType.phone:
            subtitle = 'Password via Phone';
            break;
          case LoginType.email:
            subtitle = 'Password via Email';
            break;
          default:
            subtitle = 'Password';
        }

        return Text(
          subtitle,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 28.gw,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            height: 1.2,
          ),
        );
      },
    );
  }

  /// Builds the phone input field with country code dropdown
  Widget _buildPhoneInputField(ForgotState state) {
    return PhoneInputField(
      controller: state.phoneController,
      hintText: "hint_enter_phone".tr(),
      selectedCountry: _selectedCountry,
      onCountryChanged: (country) {
        setState(() {
          _selectedCountry = country;
        });
      },
      onChanged: (value) => context.read<ForgotCubit>().setPhone(value),
    );
  }

  /// Builds the email input field
  Widget _buildEmailInputField(ForgotState state) {
    return IconTextfield(
      textController: state.emailController,
      hintText: "Enter email",
      icon: IconButton(
        icon: Image.asset(Assets.iconEmail, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<ForgotCubit>().setEmail(value),
    );
  }

  /// Builds the verification code input row
  Widget _buildVerificationCodeInputRow(ForgotState state) {
    return VerificationCodeInputRow(
      controller: state.smsCodeController,
      hintText: "Please enter the code",
      buttonText: 'get_code'.tr(),
      onButtonPressed: () {
        if (state.forgotType == LoginType.phone) {
          _sendPhoneVerificationCode(state.phone);
        } else {
          _sendEmailVerificationCode(state.email);
        }
      },
      iconAsset: Assets.iconLoginShield,
      onChanged: (value) => context.read<ForgotCubit>().setSmsCode(value),
    );
  }

  /// Builds the password input field
  Widget _buildPasswordInput(ForgotState state) {
    return IconTextfield(
      textController: TextEditingController(),
      hintText: "Password must be 6—22 letters or digits",
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<ForgotCubit>().setPassword(value),
    );
  }

  /// Builds the confirm password input field
  Widget _buildConfirmPasswordInput(ForgotState state) {
    return IconTextfield(
      textController: TextEditingController(),
      hintText: "Confirm Password",
      icon: IconButton(
        icon: Image.asset(Assets.iconLoginPassword, width: 20.gw, height: 20.gw),
        onPressed: () {},
      ),
      onChanged: (value) => context.read<ForgotCubit>().setConfirmPassword(value),
    );
  }

  /// Builds the reset button
  Widget _buildResetButton() {
    return BlocBuilder<ForgotCubit, ForgotState>(
      builder: (context, state) {
        return CommonButton(
          title: "Log In",
          textColor: context.colorTheme.btnTitlePrimary,
          showLoading: state.forgotStatus == SimplyNetStatus.loading,
          onPressed: () => context.read<ForgotCubit>().resetPassword(),
        );
      },
    );
  }

  /// Sends phone verification code
  void _sendPhoneVerificationCode(String phone) {
    if (phone.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement phone verification API call
    // Use the existing phone verification API with area code
    // API: /sms/userBindPhoneNo
    // Body: {"phoneNo": "${_selectedCountry?.areaCode}$phone"}
  }

  /// Sends email verification code
  void _sendEmailVerificationCode(String email) {
    if (email.isEmpty) {
      // Show error message
      return;
    }

    // TODO: Implement email verification API call
    // API: /mail/userBindMail
    // Body: {"mail": email, "mailCode": "verification_code"}
  }

  /// Builds a custom tab item with icon and text
  Widget _buildCustomTabItem(String title, String iconAsset, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: 100.gw,
          height: 36.gw,
          decoration: BoxDecoration(
            color: isSelected ? context.colorTheme.tabItemBgA : Colors.transparent,
            borderRadius: BorderRadius.circular(10.gw),
            border: isSelected ? Border.all(color: context.colorTheme.borderE, width: 1) : null,
          ),
          alignment: Alignment.center,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                iconAsset,
                width: 16.gw,
                height: 16.gw,
              ),
              SizedBox(width: 6.gw),
              Text(
                title,
                style: context.textTheme.primary.copyWith(
                  fontSize: 14.gw,
                  color: isSelected ? context.colorTheme.textPrimary : context.colorTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds the bottom text
  Widget _buildBottomText() {
    return GestureDetector(
      onTap: () => SystemUtil.contactService(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.gw),
        child: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: TextStyle(
              fontSize: 12.fs,
              color: Colors.grey,
            ),
            children: [
              const TextSpan(
                  text:
                      '*Only users with a bound mobile number or email can retrieve their password via self-service. Users without a bound number should contact '),
              TextSpan(
                text: 'Online Support',
                style: TextStyle(
                  color: context.theme.primaryColor,
                  decoration: TextDecoration.underline,
                ),
              ),
              const TextSpan(text: ' for assistance'),
            ],
          ),
        ),
      ),
    );
  }
}

class _ForgotHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget? title;
  final Widget? subtitle;

  _ForgotHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final progress = (shrinkOffset / (maxHeight - minHeight)).clamp(0.0, 1.0);
    final opacity = 1.0 - progress;

    return Container(
      width: double.infinity,
      height: maxHeight,
      decoration: const BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/login/bg_login_logo.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Stack(
        children: [
          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.gw,
            left: 15.gw,
            child: GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                alignment: Alignment.center,
                child: Image.asset(
                  Assets.iconBack,
                  height: 32.gh,
                  width: 32.gw,
                ),
              ),
            ),
          ),
          // Logo and title section
          Positioned.fill(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 90.gh),
                  // WD Logo
                  SvgPicture.asset(
                    Assets.tabLogo,
                    width: 131.gw,
                    height: 60.gh,
                  ),
                  SizedBox(height: 20.gh),
                  // Title
                  if (title != null) title!,
                  SizedBox(height: 8.gh),
                  // Subtitle
                  if (subtitle != null) subtitle!,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate is! _ForgotHeaderDelegate ||
        oldDelegate.minHeight != minHeight ||
        oldDelegate.maxHeight != maxHeight ||
        oldDelegate.title != title ||
        oldDelegate.subtitle != subtitle;
  }
}
