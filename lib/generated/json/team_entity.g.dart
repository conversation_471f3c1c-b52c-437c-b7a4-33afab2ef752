import 'package:wd/generated/json/base/json_convert_content.dart';
import 'package:wd/core/models/entities/team_entity.dart';

TeamEntity $TeamEntityFromJson(Map<String, dynamic> json) {
  final TeamEntity teamEntity = TeamEntity();
  final int? userTeamLevel = jsonConvert.convert<int>(json['userTeamLevel']);
  if (userTeamLevel != null) {
    teamEntity.userTeamLevel = userTeamLevel;
  }
  final List<TeamTeamConfig>? teamConfigList = (json['teamConfigList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<TeamTeamConfig>(e) as TeamTeamConfig).toList();
  if (teamConfigList != null) {
    teamEntity.teamConfigList = teamConfigList;
  }
  return teamEntity;
}

Map<String, dynamic> $TeamEntityToJson(TeamEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['userTeamLevel'] = entity.userTeamLevel;
  data['teamConfigList'] = entity.teamConfigList.map((v) => v.toJson()).toList();
  return data;
}

extension TeamEntityExtension on TeamEntity {
  TeamEntity copyWith({
    int? userTeamLevel,
    List<TeamTeamConfig>? teamConfigList,
  }) {
    return TeamEntity()
      ..userTeamLevel = userTeamLevel ?? this.userTeamLevel
      ..teamConfigList = teamConfigList ?? this.teamConfigList;
  }
}

TeamTeamConfig $TeamTeamConfigFromJson(Map<String, dynamic> json) {
  final TeamTeamConfig teamTeamConfig = TeamTeamConfig();
  final int? teamLevel = jsonConvert.convert<int>(json['teamLevel']);
  if (teamLevel != null) {
    teamTeamConfig.teamLevel = teamLevel;
  }
  final double? betRebate = jsonConvert.convert<double>(json['betRebate']);
  if (betRebate != null) {
    teamTeamConfig.betRebate = betRebate;
  }
  final double? cashinRebate = jsonConvert.convert<double>(json['cashinRebate']);
  if (cashinRebate != null) {
    teamTeamConfig.cashinRebate = cashinRebate;
  }
  final double? maxAmount = jsonConvert.convert<double>(json['maxAmount']);
  if (maxAmount != null) {
    teamTeamConfig.maxAmount = maxAmount;
  }
  return teamTeamConfig;
}

Map<String, dynamic> $TeamTeamConfigToJson(TeamTeamConfig entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['teamLevel'] = entity.teamLevel;
  data['betRebate'] = entity.betRebate;
  data['cashinRebate'] = entity.cashinRebate;
  data['maxAmount'] = entity.maxAmount;
  return data;
}

extension TeamTeamConfigExtension on TeamTeamConfig {
  TeamTeamConfig copyWith({
    int? teamLevel,
    double? betRebate,
    double? cashinRebate,
    double? maxAmount,
  }) {
    return TeamTeamConfig()
      ..teamLevel = teamLevel ?? this.teamLevel
      ..betRebate = betRebate ?? this.betRebate
      ..cashinRebate = cashinRebate ?? this.cashinRebate
      ..maxAmount = maxAmount ?? this.maxAmount;
  }
}